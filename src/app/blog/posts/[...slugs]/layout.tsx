export default function MdxLayout({ children }: { children: React.ReactNode }) {
  // Create any shared layout or styles here
  return (
    <div className="max-w-auto m-auto font-mono px-5 py-8 md:max-w-pc md:px-0 md:py-12">
      {/* <article className="prose prose-headings:mt-8 prose-headings:font-semibold prose-headings:text-black prose-h1:text-5xl prose-h2:text-4xl prose-h3:text-3xl prose-h4:text-2xl prose-h5:text-xl prose-h6:text-lg dark:prose-headings:text-white max-w-full block">
        {children}
      </article> */}
      <article className="app-prose md:prose-lg max-w-app prose-pre:bg-(--shiki-light-bg) dark:prose-pre:bg-(--shiki-dark-bg) max-w-full">
        {children}
      </article>
      {/* <article class="prose sm:prose-sm lg:prose-lg">
        {children}
      </article> */}
    </div>
  )
}
