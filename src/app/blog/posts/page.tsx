import Link from 'next/link'
import { getBlogPosts } from '@/utils/blog'

interface BlogPostMeta {
  title: string
  publishedAt: string
  summary: string
  image?: string
}

interface BlogPostItem {
  metadata: BlogPostMeta
  slug: string
  content: string
}

interface MenuNode {
  label: string
  path?: string
  children: Map<string, MenuNode>
}

function createNode(label: string): MenuNode {
  return { label, children: new Map() }
}

function insertIntoTree(root: MenuNode, post: BlogPostItem) {
  const parts = post.slug.split('/')
  let current = root

  for (let i = 0; i < parts.length; i++) {
    const isLeaf = i === parts.length - 1
    const key = parts[i]

    if (!current.children.has(key)) {
      const label = isLeaf ? post.metadata.title : key
      current.children.set(key, createNode(label))
    }

    const next = current.children.get(key)!
    if (isLeaf) next.path = post.slug
    current = next
  }
}

function buildMenuTree(posts: BlogPostItem[]): MenuNode {
  const root = createNode('root')
  posts.forEach((p) => insertIntoTree(root, p))
  return root
}

function renderTree(node: MenuNode, basePath: string) {
  const entries = Array.from(node.children.entries())
  if (entries.length === 0) return null

  return (
    <ul className="space-y-1">
      {entries.map(([key, child]) => {
        const hasChildren = child.children.size > 0
        return (
          <li key={key} className="ml-0">
            {child.path ? (
              <Link
                href={`${basePath}/${child.path}`}
                className="inline-block rounded px-2 py-1 text-sm hover:bg-gray-100 hover:underline dark:hover:bg-gray-800"
              >
                {child.label}
              </Link>
            ) : (
              <div className="select-none px-2 py-1 text-sm font-medium text-gray-700 dark:text-gray-300">
                {child.label}
              </div>
            )}
            {hasChildren && (
              <div className="ml-4 border-l pl-3">
                {renderTree(child, basePath)}
              </div>
            )}
          </li>
        )
      })}
    </ul>
  )
}

export default function Page() {
  const posts = getBlogPosts() as BlogPostItem[]
  const tree = buildMenuTree(posts)

  return (
    <div className="mx-auto max-w-pc px-4 py-6">
      <h1 className="mb-4 text-2xl font-semibold">Posts</h1>
      <nav aria-label="Posts menu tree">
        {renderTree(tree, '/blog/posts')}
      </nav>
    </div>
  )
}