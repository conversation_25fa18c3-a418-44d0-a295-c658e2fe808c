import fs from 'node:fs/promises'
import path from 'node:path'
import type { NextRequest } from 'next/server'

export const runtime = 'nodejs'
export const dynamic = 'force-dynamic'

const CONTENT_DIR = path.join(process.cwd(), 'src', 'content')
const DEFAULT_CACHE_CONTROL = 'public, max-age=3600'

function getMimeType(filePath: string): string {
  const ext = path.extname(filePath).toLowerCase()
  switch (ext) {
    case '.png':
      return 'image/png'
    case '.jpg':
    case '.jpeg':
      return 'image/jpeg'
    case '.webp':
      return 'image/webp'
    case '.gif':
      return 'image/gif'
    case '.svg':
      return 'image/svg+xml'
    case '.avif':
      return 'image/avif'
    case '.ico':
      return 'image/x-icon'
    default:
      return 'application/octet-stream'
  }
}

export async function GET(_req: NextRequest, { params }: { params: { slugs: string[] } }) {
  const { slugs: segments = [] } = await params
  // const segments = await params.slugs || []
  // segments = [ ...slugs, 'assets', ...assetPath ]
  const assetsIndex = segments.findIndex((s) => s === 'assets')
  if (assetsIndex <= 0) {
    return new Response('Not Found', { status: 404 })
  }

  const slugs = segments.slice(0, assetsIndex)
  const assetPath = segments.slice(assetsIndex + 1)

  // 将 URL 映射为: src/content/<slugs>/assets/<assetPath>
  const mdxDir = path.join(CONTENT_DIR, ...slugs)
  const fileFsPath = path.join(mdxDir, 'assets', ...assetPath)

  const resolved = path.resolve(fileFsPath)
  const withinContent = resolved.startsWith(path.resolve(CONTENT_DIR) + path.sep)
  if (!withinContent) return new Response('Forbidden', { status: 403 })

  try {
    const data = await fs.readFile(resolved)
    const contentType = getMimeType(resolved)
    return new Response(data, {
      status: 200,
      headers: {
        'Content-Type': contentType,
        'Cache-Control': DEFAULT_CACHE_CONTROL,
      },
    })
  } catch (err: unknown) {
    if ((err as NodeJS.ErrnoException)?.code === 'ENOENT') return new Response('Not Found', { status: 404 })
    return new Response('Internal Server Error', { status: 500 })
  }
} 