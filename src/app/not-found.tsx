import Link from "next/link"
export default function NotFound() {
  return (
    <div className="w-screen h-screen bg-background flex items-center justify-center">
      <div className="text-center px-4">
        <div className="mb-4">
          <h1 className="text-9xl font-bold text-muted-foreground mb-4">404</h1>
          <h2 className="text-2xl font-semibold text-foreground">Not Found!</h2>
        </div>
        
        <div className="flex flex-col sm:flex-row gap-3 justify-center">
          <Link
            href="/"
            className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2"
          >
            Back
          </Link>
        </div>
      </div>
    </div>
  )
}
