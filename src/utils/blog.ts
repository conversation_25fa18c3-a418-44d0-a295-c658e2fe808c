import fs from 'fs'
import path from 'path'

const DEFAULT_TITLE = 'Untitled'
const DEFAULT_PUBLISHED_AT = '1970-01-01'
const DEFAULT_SUMMARY = ''

type Metadata = {
  title: string
  publishedAt: string
  summary: string
  image?: string
}

function parseFrontmatter(fileContent: string): { metadata: Metadata; content: string } {
  const frontmatterRegex = /---\s*([\s\S]*?)\s*---/
  const match = frontmatterRegex.exec(fileContent)

  let content = ''
  const metadata: Partial<Metadata> = {
    title: DEFAULT_TITLE,
    publishedAt: DEFAULT_PUBLISHED_AT,
    summary: DEFAULT_SUMMARY,
  }

  if (match) {
    const frontMatterBlock = match[1]
    content = fileContent.replace(frontmatterRegex, '').trim()
    const frontMatterLines = frontMatterBlock.trim().split('\n')

    frontMatterLines.forEach((line) => {
      const [key, ...valueArr] = line.split(': ')
      if (!key) return
      let value = valueArr.join(': ').trim()
      value = value.replace(/^[\"'](.*)[\"']$/, '$1')
      ;(metadata as any)[key.trim()] = value
    })
  } else {
    content = fileContent.trim()
    const h1 = extractFirstHeading(content)
    const para = extractFirstParagraph(content)
    if (h1) metadata.title = h1
    if (para) metadata.summary = para
  }

  return { metadata: metadata as Metadata, content }
}

function extractFirstHeading(content: string): string | null {
  const m = /^#\s+(.+?)\s*$/m.exec(content)
  return m ? m[1] : null
}

function extractFirstParagraph(content: string): string | null {
  const blocks = content.split(/\n\s*\n/)
  for (const block of blocks) {
    const trimmed = block.trim()
    if (!trimmed) continue
    if (/^\s*(#|>|-|\d+\.|\*\s)/.test(trimmed)) continue
    return trimmed.replace(/\s+/g, ' ').slice(0, 200)
  }
  return null
}

function getMDXFiles(dir: string): string[] {
  return fs.readdirSync(dir).filter((file) => path.extname(file) === '.mdx')
}

function readMDXFile(filePath: string): { metadata: Metadata; content: string } {
  const rawContent = fs.readFileSync(filePath, 'utf-8')
  return parseFrontmatter(rawContent)
}

function getMDXFilesRecursive(dir: string): string[] {
  const entries = fs.readdirSync(dir, { withFileTypes: true })
  const files: string[] = []

  for (const entry of entries) {
    const fullPath = path.join(dir, entry.name)
    if (entry.isDirectory()) {
      files.push(...getMDXFilesRecursive(fullPath))
    } else if (entry.isFile() && path.extname(entry.name) === '.mdx') {
      files.push(fullPath)
    }
  }

  return files
}

function getMDXData(dir: string): Array<{ metadata: Metadata; slug: string; content: string }> {
  const mdxFiles = getMDXFilesRecursive(dir)
  return mdxFiles.map((file) => {
    const { metadata, content } = readMDXFile(file)
    const relativePath = path.relative(dir, file)
    const slug = relativePath.replace(/\\/g, '/').replace(path.extname(relativePath), '')

    return {
      metadata,
      slug,
      content,
    }
  })
}

export function getBlogPosts({ slug }: { slug?: string } = {}): Array<{ metadata: Metadata; slug: string; content: string }> {
  // if (slug) {
  //   const slugs = slug.split('/')
  //   const file = getMDXFile(path.join(process.cwd(), 'src', 'content', ...slugs))
  //   const { metadata, content } = readMDXFile(file)
  //   return [{
  //     metadata,
  //     slug,
  //     content,
  //   }]
  // }
  return getMDXData(path.join(process.cwd(), 'src', 'content'))
}

export function formatDate(date: string, includeRelative = false) {
  const currentDate = new Date()
  if (!date.includes('T')) {
    date = `${date}T00:00:00`
  }
  const targetDate = new Date(date)

  const yearsAgo = currentDate.getFullYear() - targetDate.getFullYear()
  const monthsAgo = currentDate.getMonth() - targetDate.getMonth()
  const daysAgo = currentDate.getDate() - targetDate.getDate()

  let formattedDate = ''

  if (yearsAgo > 0) {
    formattedDate = `${yearsAgo}y ago`
  } else if (monthsAgo > 0) {
    formattedDate = `${monthsAgo}mo ago`
  } else if (daysAgo > 0) {
    formattedDate = `${daysAgo}d ago`
  } else {
    formattedDate = 'Today'
  }

  const fullDate = targetDate.toLocaleString('en-us', {
    month: 'long',
    day: 'numeric',
    year: 'numeric',
  })

  if (!includeRelative) {
    return fullDate
  }

  return `${fullDate} (${formattedDate})`
}
